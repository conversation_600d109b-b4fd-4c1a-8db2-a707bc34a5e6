/**
 * @file SimpleRWSStrategy.cpp
 * @brief 简化版简单读写策略实现
 *
 * <AUTHOR> Framework
 * @date 2025-01-19
 */

#include "SimpleRWSStrategy.h"
#include <QDebug>

SimpleRWSStrategy::SimpleRWSStrategy(const SimpleConfig &config)
    : m_config(config)
{
    qDebug() << "SimpleRWSStrategy: 初始化完成";
}

SimpleRWSStrategy::~SimpleRWSStrategy()
{
    qDebug() << "SimpleRWSStrategy: 析构";
}

QByteArray SimpleRWSStrategy::wrapMessage(const QByteArray &message)
{
    // 简单策略直接返回原始数据，不做任何封装
    return message;
}

QList<QByteArray> SimpleRWSStrategy::processReceivedData(const QByteArray &newData)
{
    if (newData.isEmpty()) {
        return QList<QByteArray>();
    }

    // 添加新数据到缓冲区
    m_receiveBuffer.append(newData);

    // 根据配置进行分块处理
    QList<QByteArray> result;
    if (m_config.chunkSize > 0 && m_receiveBuffer.size() > m_config.chunkSize) {
        result = chunkData(m_receiveBuffer);
        m_receiveBuffer.clear();
    } else {
        // 直接返回所有数据
        result.append(m_receiveBuffer);
        m_receiveBuffer.clear();
    }

    return result;
}

void SimpleRWSStrategy::clearBuffer()
{
    m_receiveBuffer.clear();
    qDebug() << "SimpleRWSStrategy: 缓冲区已清空";
}

QList<QByteArray> SimpleRWSStrategy::chunkData(const QByteArray &data)
{
    QList<QByteArray> chunks;

    int offset = 0;
    while (offset < data.size()) {
        int chunkSize = qMin(m_config.chunkSize, data.size() - offset);
        QByteArray chunk = data.mid(offset, chunkSize);
        chunks.append(chunk);
        offset += chunkSize;
    }

    return chunks;
}


