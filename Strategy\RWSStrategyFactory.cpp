/**
 * @file RWSStrategyFactory.cpp
 * @brief 简化版读写策略工厂类实现
 *
 * <AUTHOR> Framework
 * @date 2025-01-19
 */

#include "RWSStrategyFactory.h"
#include "PreLengthRWSStrategy.h"
#include "EndWithNewLineRWSStrategy.h"
#include "SimpleRWSStrategy.h"
#include <QDebug>

std::unique_ptr<IRWSStrategy> RWSStrategyFactory::createStrategy(IRWSStrategy::StrategyType type)
{
    switch (type) {
    case IRWSStrategy::PreLength:
        return std::make_unique<PreLengthRWSStrategy>(PreLengthConfig());
    case IRWSStrategy::EndWithNewLine:
        return std::make_unique<EndWithNewLineRWSStrategy>(EndWithNewLineConfig());
    case IRWSStrategy::Simple:
        return std::make_unique<SimpleRWSStrategy>(SimpleConfig());
    default:
        qWarning() << "RWSStrategyFactory: 不支持的策略类型:" << static_cast<int>(type);
        return nullptr;
    }
}

std::unique_ptr<IRWSStrategy> RWSStrategyFactory::createStrategy(const RWSConfig &config)
{
    switch (config.strategyType()) {
    case IRWSStrategy::PreLength:
        return std::make_unique<PreLengthRWSStrategy>(config.preLengthConfig());
    case IRWSStrategy::EndWithNewLine:
        return std::make_unique<EndWithNewLineRWSStrategy>(config.endWithNewLineConfig());
    case IRWSStrategy::Simple:
        return std::make_unique<SimpleRWSStrategy>(config.simpleConfig());
    default:
        qWarning() << "RWSStrategyFactory: 不支持的策略类型:" << static_cast<int>(config.strategyType());
        return nullptr;
    }
}

QList<IRWSStrategy::StrategyType> RWSStrategyFactory::getSupportedStrategyTypes()
{
    return {
        IRWSStrategy::PreLength,
        IRWSStrategy::EndWithNewLine,
        IRWSStrategy::Simple
    };
}

bool RWSStrategyFactory::isStrategySupported(IRWSStrategy::StrategyType type)
{
    return getSupportedStrategyTypes().contains(type);
}


