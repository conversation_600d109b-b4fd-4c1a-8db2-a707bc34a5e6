#ifndef END_WITH_NEW_LINE_RWS_STRATEGY_H
#define END_WITH_NEW_LINE_RWS_STRATEGY_H

#include "IRWSStrategy.h"
#include "RWSConfig.h"
#include <QByteArray>

/**
 * @brief 简化版换行符结束读写策略类
 */
class EndWithNewLineRWSStrategy : public IRWSStrategy
{

public:
    /**
     * @brief 构造函数
     */
    explicit EndWithNewLineRWSStrategy(const EndWithNewLineConfig &config = EndWithNewLineConfig());

    /**
     * @brief 析构函数
     */
    ~EndWithNewLineRWSStrategy() override;

    // ========== IRWSStrategy接口实现 ==========

    QByteArray wrapMessage(const QByteArray &message) override;
    QList<QByteArray> processReceivedData(const QByteArray &newData) override;
    void clearBuffer() override;
    QString strategyName() const override { return "EndWithNewLineRWS"; }
    StrategyType strategyType() const override { return EndWithNewLine; }

    // ========== 协议常量 ==========
    static const QByteArray DEFAULT_DELIMITER;         // 默认分隔符 "\n"

private:
    /**
     * @brief 从缓冲区中提取完整行
     */
    QList<QByteArray> extractCompleteLines();

    /**
     * @brief 验证行长度
     */
    bool isValidLineLength(int lineLength) const;

private:
    EndWithNewLineConfig m_config;              // 策略配置
    QByteArray m_receiveBuffer;                 // 接收缓冲区
};

#endif // END_WITH_NEW_LINE_RWS_STRATEGY_H
