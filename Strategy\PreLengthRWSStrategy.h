#ifndef PRE_LENGTH_RWS_STRATEGY_H
#define PRE_LENGTH_RWS_STRATEGY_H

#include "IRWSStrategy.h"
#include "RWSConfig.h"
#include <QByteArray>

/**
 * @brief 简化版预长度读写策略类
 */
class PreLengthRWSStrategy : public IRWSStrategy
{

public:
    /**
     * @brief 构造函数
     */
    explicit PreLengthRWSStrategy(const PreLengthConfig &config = PreLengthConfig());

    /**
     * @brief 析构函数
     */
    ~PreLengthRWSStrategy() override;

    // ========== IRWSStrategy接口实现 ==========

    QByteArray wrapMessage(const QByteArray &message) override;
    QList<QByteArray> processReceivedData(const QByteArray &newData) override;
    void clearBuffer() override;
    QString strategyName() const override { return "PreLengthRWS"; }
    StrategyType strategyType() const override { return PreLength; }

    // ========== 协议常量 ==========
    static const int LENGTH_HEADER_SIZE = 4;           // 长度头大小（字节）

private:
    /**
     * @brief 从缓冲区中提取完整消息
     */
    QList<QByteArray> extractCompleteMessages();

    /**
     * @brief 验证消息长度
     */
    bool isValidMessageLength(quint32 length) const;

private:
    PreLengthConfig m_config;                   // 策略配置
    QByteArray m_receiveBuffer;                 // 接收缓冲区
};

#endif // PRE_LENGTH_RWS_STRATEGY_H
