#include "messagedata.h"
#include <QMessageBox>
#include <QDateTime>
#include <QDate>
#include <QFile>
#include <QTextStream>
#include <QPainter>
#include <QDir>
#include <iostream>
#include "logger/logger.h"

QString iconToString(QMessageBox::Icon icon) {
    switch (icon) {
    case QMessageBox::NoIcon:
        return "NoIcon";
    case QMessageBox::Information:
        return "Information";
    case QMessageBox::Warning:
        return "Warning";
    case QMessageBox::Critical:
        return "Critical";
    case QMessageBox::Question:
        return "Question";
    default:
        return "";
    }
}

MessageData::MessageData(QObject *parent):QObject(parent)
{
    m_msgModel=MsgModel::Widget;
    m_logPath="";
    flashTimer=new QTimer(this);
    whiteColorBg=QString("background-color: %1;").arg(Qt::white);
    flashColor=QColor(Qt::red).name();
    connect(flashTimer,&QTimer::timeout,this,[=](){
        if(labelMessage){
            if(flashCount>=maxFlashes*2){
                flashTimer->stop();
                QString orgin=QString("background-color: %1;").arg(m_originColor.name());
                labelMessage->setStyleSheet(orgin);
                labelMessage->setText("");
                flashCount=0;
            }else{
                labelMessage->setText(m_info);
                if (flashCount % 2 == 0) {
                    QString color=QString("background-color: %1;").arg(m_color.name());
                    labelMessage->setStyleSheet(color);
                } else {
                    if(m_color!=m_originColor)
                        labelMessage->setStyleSheet(whiteColorBg);
                }
                flashCount++;
            }
        }else{
            flashTimer->stop();
            flashCount=0;
        }
    });

}

void MessageData::setWidget(QLabel *label)
{
    labelMessage=label;
    m_font=labelMessage->font();
    m_originColor=labelMessage->palette().color(QPalette::Window);
}

void MessageData::setLogPath(QString logPath)
{
    if(logPath.isEmpty())
        return;
    QDir d(logPath);
    if(!d.exists())
        d.mkpath(logPath);
    m_logPath=logPath;
    Logger::init(m_logPath);
}

void MessageData::setMsgShowModel(int msgModel)
{
    m_msgModel=msgModel;
}

LogLevel MessageLevelTLogLevel(const QMessageBox::Icon level){
   LogLevel lv=LogLevel::Info;
    switch(level){
    case QMessageBox::Icon::Information:
        lv=LogLevel::Info;
        break;
    case QMessageBox::Icon::Critical:
        lv=LogLevel::Error;
        break;
    case  QMessageBox::Icon::Warning:
        lv=LogLevel::Warn;
        break;
    default:
        lv=LogLevel::Info;
        break;
    }
    return lv;
}

void MessageData::receiveMsg(QMessageBox::Icon level, QString moduleName, QString info,bool messageBox)
{
    QString currentT=QDateTime::currentDateTime().toString("yyyy_MM_dd hh:mm:ss");
    m_msg.push_back(_msg{m_msgModel,level,moduleName,info});
    if(m_msgModel&MsgModel::Console)
    {
        std::cout<<currentT.toStdString()<<" "<<iconToString(level).toStdString()<<" "<<moduleName.toUtf8().toStdString()<<" "<<info.toUtf8().toStdString();
    }
    if(m_msgModel&MsgModel::Log)
    {
        if(d_ptr){
            Logger::getInstance().log(MessageLevelTLogLevel(level),moduleName,info);
        }else{
            if(labelMessage){
                showLabelMsg(5,Qt::red,tr("未设置log保存路径!"));
                return;
            }
        }
    }
    if(m_msgModel&MsgModel::Widget)
    {
        switch(level){
        case QMessageBox::Icon::Information:
            showLabelMsg(3,m_originColor,moduleName+" "+info);
            break;
        case QMessageBox::Icon::Critical:
            showLabelMsg(5,Qt::red,moduleName+" "+info);
            break;
        case  QMessageBox::Icon::Warning:
            showLabelMsg(5,Qt::yellow,moduleName+" "+info);
            break;
        default:
            showLabelMsg(5,m_originColor,moduleName+" "+info);
            break;
        }
    }
    if(m_msgModel&MsgModel::MessageBox||messageBox)
    {
        switch(level){
        case QMessageBox::Icon::Information:
            QMessageBox::information(NULL,moduleName,info);
            break;
        case QMessageBox::Icon::Critical:
            QMessageBox::critical(NULL,moduleName,info);
            break;
        case  QMessageBox::Icon::Warning:
            QMessageBox::warning(NULL,moduleName,info);
            break;
        default:
            QMessageBox::information(NULL,moduleName,info);
            break;
        }
    }
}

void MessageData::showLabelMsg(int flashNumber, QColor bgColor, const QString &info)
{
    maxFlashes=flashNumber;
    m_color=bgColor;
    m_info=info;
    flashCount=0;
    flashTimer->start(300);
}

