﻿#ifndef CMDERRORCODE_H
#define CMDERRORCODE_H
#include <QObject>
#include "network_global.h"

class  NETWORK_EXPORT CmdErrorCode:public QObject
{
    Q_OBJECT
public:
    ~CmdErrorCode();
    static CmdErrorCode&getInstance();

    QString ErroCode2QString(int code);
private:
    static CmdErrorCode instance;
    CmdErrorCode();
    CmdErrorCode(CmdErrorCode&other)=delete;
    CmdErrorCode &operator=(const CmdErrorCode&)=delete;
};

#endif // CMDERRORCODE_H
