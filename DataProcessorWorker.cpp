#include "DataProcessorWorker.h"
#include <QDebug>
#include <QMutexLocker>
#include <QUuid>
#include <QDateTime>
#include <QThread>
#include <QJsonArray>

DataProcessorWorker::DataProcessorWorker(QObject *parent)
    : QObject(parent)
    , m_textProcesser(nullptr)
    , m_lastError(ProcessError::NoError)
    , m_initialized(0)
    , m_shouldStop(0)
    , m_messagesProcessed(0)
    , m_errorsCount(0)
    , m_startTime(0)
{
    qDebug() << "DataProcessorWorker: Created in thread" << QThread::currentThread();
}

DataProcessorWorker::~DataProcessorWorker()
{
    qDebug() << "DataProcessorWorker: Destructor called in thread" << QThread::currentThread();
    
    // 确保清理工作在正确的线程中完成
    if (m_textProcesser) {
        qWarning() << "DataProcessorWorker: Destructor called before cleanup() - this may cause issues";
    }
}

void DataProcessorWorker::initialize()
{
    qDebug() << "DataProcessorWorker: Initializing in thread" << QThread::currentThread();
    
    bool success = safeExecute([this]() {
        // 创建ada3文本处理器，使用压缩配置
        m_textProcesser = new utl::TinyTextProcesser({utl::TinyTextProcesser::Manipulation::Compress});
        
        // 初始化统计信息
        {
            QMutexLocker locker(&m_statsMutex);
            m_messagesProcessed = 0;
            m_errorsCount = 0;
            m_startTime = QDateTime::currentMSecsSinceEpoch();
        }
        
        m_initialized.storeRelaxed(1);
        setError(ProcessError::NoError, QString());
        
    }, "initialization");
    
    if (success) {
        qDebug() << "DataProcessorWorker: Initialization completed successfully";
        emit initialized();
    } else {
        setError(ProcessError::InitializationError, "Failed to initialize data processor");
        emit processingError(m_lastError, m_errorString);
    }
}

void DataProcessorWorker::cleanup()
{
    qDebug() << "DataProcessorWorker: Cleanup started in thread" << QThread::currentThread();
    
    // 设置停止标志
    m_shouldStop.storeRelaxed(1);
    m_initialized.storeRelaxed(0);
    
    // 清理文本处理器
    if (m_textProcesser) {
        delete m_textProcesser;
        m_textProcesser = nullptr;
    }
    
    qDebug() << "DataProcessorWorker: Cleanup completed";
    emit cleanupCompleted();
}

void DataProcessorWorker::processRawData(const QByteArray &rawData)
{
    if (m_shouldStop.loadRelaxed()) {
        return;
    }
    
    if (!m_initialized.loadRelaxed()) {
        setError(ProcessError::InitializationError, "Data processor not initialized");
        emit processingError(m_lastError, m_errorString);
        return;
    }
    
    if (rawData.isEmpty()) {
        return;
    }
    
    bool success = safeExecute([this, &rawData]() {
        // 解码数据
        QJsonObject json;
        {
            // 内联解码逻辑以避免方法调用问题
            if (rawData.isEmpty()) {
                setError(ProcessError::InvalidMessageFormat, "Empty raw data");
                emit processingError(m_lastError, m_errorString);
                return;
            }

            QByteArray decompressedData;
            if (m_textProcesser) {
                try {
                    decompressedData = m_textProcesser->unDeal(rawData);
                } catch (const std::exception &e) {
                    setError(ProcessError::DecompressionError,
                            QString("Decompression failed: %1").arg(e.what()));
                    emit processingError(m_lastError, m_errorString);
                    return;
                } catch (...) {
                    setError(ProcessError::UnknownError, "Unknown exception during decompression");
                    emit processingError(m_lastError, m_errorString);
                    return;
                }
            } else {
                decompressedData = qUncompress(rawData);
                if (decompressedData.isEmpty()) {
                    setError(ProcessError::DecompressionError, "Qt decompression failed");
                    emit processingError(m_lastError, m_errorString);
                    return;
                }
            }

            QJsonDocument doc = QJsonDocument::fromBinaryData(decompressedData);
            if (!doc.isObject()) {
                setError(ProcessError::JsonParseError, "Invalid JSON format");
                emit processingError(m_lastError, m_errorString);
                return;
            }

            json = doc.object();
        }

        if (json.isEmpty()) {
            return;
        }
        
        // 处理消息
        processSingleMessage(json);
        
        // 更新统计信息
        {
            QMutexLocker locker(&m_statsMutex);
            m_messagesProcessed++;
        }
        
    }, "raw data processing");
    
    if (!success) {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }
}

void DataProcessorWorker::encodeJsonData(const QJsonObject &json)
{
    if (m_shouldStop.loadRelaxed()) {
        return;
    }
    
    if (!m_initialized.loadRelaxed()) {
        setError(ProcessError::InitializationError, "Data processor not initialized");
        emit encodingError(m_lastError, m_errorString, json);
        return;
    }
    
    bool success = safeExecute([this, &json]() {
        // 内联编码逻辑
        QJsonDocument doc(json);
        QByteArray binaryData = doc.toBinaryData();
        QByteArray encodedData;

        if (m_textProcesser) {
            try {
                encodedData = m_textProcesser->deal(binaryData);
                setError(ProcessError::NoError, QString());
            } catch (const std::exception &e) {
                setError(ProcessError::DecompressionError,
                        QString("Compression failed: %1").arg(e.what()));
                emit encodingError(m_lastError, m_errorString, json);
                return;
            } catch (...) {
                setError(ProcessError::UnknownError, "Unknown exception during compression");
                emit encodingError(m_lastError, m_errorString, json);
                return;
            }
        } else {
            encodedData = qCompress(binaryData);
            setError(ProcessError::NoError, QString());
        }

        if (!encodedData.isEmpty()) {
            emit jsonEncoded(encodedData, json);
        }

    }, "JSON encoding");
    
    if (!success) {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }
}

void DataProcessorWorker::decodeRawData(const QByteArray &rawData)
{
    if (m_shouldStop.loadRelaxed()) {
        return;
    }
    
    if (!m_initialized.loadRelaxed()) {
        setError(ProcessError::InitializationError, "Data processor not initialized");
        emit decodingError(m_lastError, m_errorString, rawData);
        return;
    }
    
    if (rawData.isEmpty()) {
        setError(ProcessError::InvalidMessageFormat, "Empty raw data");
        emit decodingError(m_lastError, m_errorString, rawData);
        return;
    }
    
    bool success = safeExecute([this, &rawData]() {
        // 内联解码逻辑
        QByteArray decompressedData;
        if (m_textProcesser) {
            try {
                decompressedData = m_textProcesser->unDeal(rawData);
            } catch (const std::exception &e) {
                setError(ProcessError::DecompressionError,
                        QString("Decompression failed: %1").arg(e.what()));
                emit decodingError(m_lastError, m_errorString, rawData);
                return;
            } catch (...) {
                setError(ProcessError::UnknownError, "Unknown exception during decompression");
                emit decodingError(m_lastError, m_errorString, rawData);
                return;
            }
        } else {
            decompressedData = qUncompress(rawData);
            if (decompressedData.isEmpty()) {
                setError(ProcessError::DecompressionError, "Qt decompression failed");
                emit decodingError(m_lastError, m_errorString, rawData);
                return;
            }
        }

        QJsonDocument doc = QJsonDocument::fromBinaryData(decompressedData);
        if (!doc.isObject()) {
            setError(ProcessError::JsonParseError, "Invalid JSON format");
            emit decodingError(m_lastError, m_errorString, rawData);
            return;
        }

        QJsonObject decodedJson = doc.object();
        setError(ProcessError::NoError, QString());
        emit dataDecoded(decodedJson, rawData);

    }, "raw data decoding");
    
    if (!success) {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }
}

void DataProcessorWorker::stop()
{
    qDebug() << "DataProcessorWorker: Stop requested";
    m_shouldStop.storeRelaxed(1);
}

DataProcessorWorker::ProcessError DataProcessorWorker::lastError() const
{
    QMutexLocker locker(&m_errorMutex);
    return m_lastError;
}

QString DataProcessorWorker::errorString() const
{
    QMutexLocker locker(&m_errorMutex);
    return m_errorString;
}

bool DataProcessorWorker::isInitialized() const
{
    return m_initialized.loadRelaxed() == 1;
}

// ========== 私有方法实现 ==========

void DataProcessorWorker::processSingleMessage(const QJsonObject &json)
{
    // 验证JSON结构
    if (!validateJsonStructure(json)) {
        setError(ProcessError::InvalidMessageFormat, "Invalid JSON message structure");
        emit processingError(m_lastError, m_errorString);
        return;
    }

    // 提取UUID
    QUuid uuid = extractUuid(json);

    // 识别消息类型
    QString messageType = identifyMessageType(json);

    if (messageType.isEmpty()) {
        setError(ProcessError::UnsupportedMessageType, "Unknown message type");
        emit processingError(m_lastError, m_errorString);
        return;
    }

    // 尝试使用ada3的命令工厂解析
    bool isCommand = false;
    bool isReply = false;

    try {
        cmd::CommandWrap command(cmd::cmdFactory().fromJsonObj(json));
        if (!command.isNull()) {
            isCommand = true;
        }
    } catch (const std::exception &e) {
        qDebug() << "DataProcessorWorker: Command parsing failed:" << e.what();
    } catch (...) {
        qDebug() << "DataProcessorWorker: Command parsing failed with unknown exception";
    }

    // 如果不是命令，尝试解析为Reply
    if (!isCommand) {
        try {
            // 检查是否是Reply类型的消息
            if (json.contains("Name") && json.contains("Module")) {
                QString name = json.value("Name").toString();
                QString module = json.value("Module").toString();

                if (module == "reply" || name.contains("Reply", Qt::CaseInsensitive)) {
                    isReply = true;
                }
            }
        } catch (const std::exception &e) {
            qDebug() << "DataProcessorWorker: Reply parsing failed:" << e.what();
        } catch (...) {
            qDebug() << "DataProcessorWorker: Reply parsing failed with unknown exception";
        }
    }

    // 发送相应的信号
    if (isCommand) {
        emit commandReceived(json, uuid);
    } else if (isReply) {
        emit replyReceived(json, uuid);
    } else {
        // 根据消息类型进行最佳猜测
        if (messageType.contains("Reply", Qt::CaseInsensitive) ||
            messageType.contains("Response", Qt::CaseInsensitive)) {
            emit replyReceived(json, uuid);
        } else {
            emit commandReceived(json, uuid);
        }
    }
}

QString DataProcessorWorker::identifyMessageType(const QJsonObject &json)
{
    // 检查Name字段
    if (json.contains("Name")) {
        QString name = json.value("Name").toString();
        if (!name.isEmpty()) {
            return name;
        }
    }

    // 检查Type字段
    if (json.contains("Type")) {
        QString type = json.value("Type").toString();
        if (!type.isEmpty()) {
            return type;
        }
    }

    // 检查Module字段
    if (json.contains("Module")) {
        QString module = json.value("Module").toString();
        if (!module.isEmpty()) {
            return module;
        }
    }

    // 检查Command字段
    if (json.contains("Command")) {
        QString command = json.value("Command").toString();
        if (!command.isEmpty()) {
            return command;
        }
    }

    return QString();
}

QUuid DataProcessorWorker::extractUuid(const QJsonObject &json)
{
    // 检查Uuid字段
    if (json.contains("Uuid")) {
        QString uuidStr = json.value("Uuid").toString();
        if (!uuidStr.isEmpty()) {
            QUuid uuid(uuidStr);
            if (!uuid.isNull()) {
                return uuid;
            }
        }
    }

    // 检查UUID字段（大写）
    if (json.contains("UUID")) {
        QString uuidStr = json.value("UUID").toString();
        if (!uuidStr.isEmpty()) {
            QUuid uuid(uuidStr);
            if (!uuid.isNull()) {
                return uuid;
            }
        }
    }

    // 检查Id字段
    if (json.contains("Id")) {
        QString idStr = json.value("Id").toString();
        if (!idStr.isEmpty()) {
            QUuid uuid(idStr);
            if (!uuid.isNull()) {
                return uuid;
            }
        }
    }

    return QUuid();
}

void DataProcessorWorker::setError(ProcessError error, const QString &errorString)
{
    QMutexLocker locker(&m_errorMutex);
    m_lastError = error;
    m_errorString = errorString;
}

bool DataProcessorWorker::safeExecute(std::function<void()> operation, const QString &operationName)
{
    try {
        operation();
        return true;
    } catch (const std::exception &e) {
        QString errorMsg = QString("Exception in %1: %2").arg(operationName, e.what());
        qWarning() << "DataProcessorWorker:" << errorMsg;
        setError(ProcessError::UnknownError, errorMsg);
        return false;
    } catch (...) {
        QString errorMsg = QString("Unknown exception in %1").arg(operationName);
        qWarning() << "DataProcessorWorker:" << errorMsg;
        setError(ProcessError::UnknownError, errorMsg);
        return false;
    }
}

bool DataProcessorWorker::validateJsonStructure(const QJsonObject &json)
{
    // 基本验证：JSON对象不能为空
    if (json.isEmpty()) {
        return false;
    }

    // 检查是否包含基本的识别字段之一
    QStringList requiredFields = {"Name", "Type", "Module", "Command"};
    for (const QString &field : requiredFields) {
        if (json.contains(field)) {
            return true;
        }
    }

    // 如果没有标准字段，但有其他内容，也认为是有效的
    return !json.keys().isEmpty();
}
