#ifndef IRWS_STRATEGY_H
#define IRWS_STRATEGY_H

#include <QObject>
#include <QByteArray>
#include <QList>
#include <QString>

/**
 * @brief 简化版读写策略接口
 *
 * 定义了数据传输协议的基本接口
 */
class IRWSStrategy
{
public:
    /**
     * @brief 策略类型枚举
     */
    enum StrategyType {
        PreLength,          // 预长度策略
        EndWithNewLine,     // 换行符结束策略
        Simple              // 简单策略
    };

    /**
     * @brief 虚析构函数
     */
    virtual ~IRWSStrategy() = default;

    /**
     * @brief 封装消息用于发送
     * @param message 原始消息数据
     * @return 封装后的数据
     */
    virtual QByteArray wrapMessage(const QByteArray &message) = 0;

    /**
     * @brief 处理接收到的数据，解析出完整消息
     * @param newData 新接收的数据
     * @return 解析出的完整消息列表
     */
    virtual QList<QByteArray> processReceivedData(const QByteArray &newData) = 0;

    /**
     * @brief 清空接收缓冲区
     */
    virtual void clearBuffer() = 0;

    /**
     * @brief 获取策略名称
     * @return 策略名称
     */
    virtual QString strategyName() const = 0;

    /**
     * @brief 获取策略类型
     * @return 策略类型
     */
    virtual StrategyType strategyType() const = 0;
};

/**
 * @brief 简化版策略工具类
 */
class RWSStrategyUtils
{
public:
    /**
     * @brief 策略类型转字符串
     */
    static QString strategyTypeToString(IRWSStrategy::StrategyType type);

    /**
     * @brief 字符串转策略类型
     */
    static IRWSStrategy::StrategyType stringToStrategyType(const QString &str);
};

#endif // IRWS_STRATEGY_H
