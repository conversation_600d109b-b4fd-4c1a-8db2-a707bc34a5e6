﻿#ifndef NETWORK_GLOBAL_H
#define NETWORK_GLOBAL_H

#if defined(_MSC_VER) || defined(WIN64) || defined(_WIN64) || defined(__WIN64__) || defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__)
#  define Q_DECL_EXPORT __declspec(dllexport)
#  define Q_DECL_IMPORT __declspec(dllimport)
#else
#  define Q_DECL_EXPORT     __attribute__((visibility("default")))
#  define Q_DECL_IMPORT     __attribute__((visibility("default")))
#endif

#if defined(NETWORK_LIBRARY)
#  define NETWORK_EXPORT Q_DECL_EXPORT
#elif (NETWORK_SRC)
#  define NETWORK_EXPORT
#else
#  define NETWORK_LIBRARY Q_DECL_IMPORT
#endif

#endif // NETWORK_GLOBAL_H
