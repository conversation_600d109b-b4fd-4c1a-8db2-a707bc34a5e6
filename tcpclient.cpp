﻿#include "tcpclient.h"

#include "cmd/addtcpserver.h"
#include "cmd/srvstart.h"
#include "cmd/iodevadd.h"
#include "cmd/iodevconnecthost.h"
#include "cmd/iodevwrite.h"
#include "cmd/srvsendmsg.h"
#include "iodev/prelengthrws.h"
#include "iodev/simplerws.h"
#include "cmd/iodevset.h"
#include "cmd/iodevopen.h"
#include "cmd/iodevwrite.h"
#include "cmd/modbuscliadd.h"
#include "cmd/modbuscliconn.h"
#include "cmd/modbusclidisconn.h"
#include "cmd/analogadd.h"
#include "cmd/modbuscliwrite.h"

TcpClient::TcpClient(const QString &appName, QObject *parent)
    :QObject(parent),
    m_appName(appName),
    bStart(true)
{
    m_commandTrans=new CommandTransceiverRefactored(this);
    connect(m_commandTrans,&CommandTransceiverRefactored::connectionStateChanged,this,&TcpClient::onConnectionStateChanged);
    connect(m_commandTrans,&CommandTransceiverRefactored::commandResponseReceived,this,&TcpClient::onCommandResponseReceived);
    packetThread=new QThread(this);
    this->moveToThread(packetThread);
    connect(packetThread,&QThread::started,this,&TcpClient::packetProcessorThread);
    packetThread->start();

}

TcpClient::~TcpClient()
{
    bStart=false;
    m_commandTrans->deleteLater();
    m_commandTrans=nullptr;
    if(packetThread)
    {
        packetThread->quit();
        packetThread->wait();
    }
}

bool TcpClient::connectToServer(const QString &host, quint16 port, int timeoutMs)
{
    CommandTransceiverRefactored::ConnectionConfig connectConfig;
    connectConfig.serverAddress=QHostAddress(host);
    connectConfig.serverPort=port;
    connectConfig.connectTimeoutMs=timeoutMs;
    return m_commandTrans->connectToServer(connectConfig);
}

void TcpClient::disconnectFromServer()
{
    m_commandTrans->disconnectFromServer();
}

bool TcpClient::isConnected() const
{
    return m_commandTrans->isConnected();
}

void TcpClient::writeTcpCfg(const QJsonObject &msg)
{
    if(m_commandTrans){
        if(isConnected()){
            QMutexLocker lock(&m_commandMutex);
            m_commandTrans->sendCommand(msg);
        }else{
            QMutexLocker lock(&m_configMutex);
            configQueue.enqueue(msg);
        }
    }
}

void TcpClient::writeTcpAction(const QJsonObject &msg)
{
    if(m_commandTrans && isConnected()){
        QMutexLocker lock(&m_commandMutex);
        m_commandTrans->sendCommand(msg);
    }
}

void TcpClient::onConnectionStateChanged(CommandTransceiverRefactored::ConnectionState state)
{
    bool connected = (state == CommandTransceiverRefactored::ConnectionState::Connected);
    emit connectionStatusChanged(connected);
    
    if(connected) {
        processCachedCommands();
    }
}

void TcpClient::onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response)
{
    QMutexLocker locker(&m_statusMutex);
    switch (response.result) {
    case CommandTransceiverRefactored::CommandResult::Success:
        packetQueue.append(response.data);
        break;
    case CommandTransceiverRefactored::CommandResult::Timeout:
        emit sigMsg(QMessageBox::Critical,tr("操作超时"),"TCP",false);
        break;
    case CommandTransceiverRefactored::CommandResult::NetworkError:
        emit sigMsg(QMessageBox::Critical,tr("网络错误"),"TCP",false);
        break;
    case CommandTransceiverRefactored::CommandResult::ServerError:
        emit sigMsg(QMessageBox::Critical,tr("服务器错误"),"TCP",false);
        break;
    default:
        emit sigMsg(QMessageBox::Critical,tr("未知错误"),"TCP",false);
        break;
    }
}


void TcpClient::processCachedCommands()
{
    QMutexLocker lock(&m_configMutex);
    while(!configQueue.isEmpty()){
        QJsonObject msg = configQueue.dequeue();
        if(m_commandTrans){
            QMutexLocker cmdLock(&m_commandMutex);
            m_commandTrans->sendCommand(msg);
        }
    }
}

void TcpClient::packetProcessorThread()
{
        while(bStart){
            QJsonObject packet;
            m_statusMutex.lock();
            if(!packetQueue.isEmpty()){
                packet=packetQueue.dequeue();
            }
            m_statusMutex.unlock();
            if(!packet.isEmpty()){
                auto *reply=cmd::replyFactory().fromJsonObj(packet);
                if(reply)
                {
                    if(reply->name()==cmd::FlowStatusReply::name())
                    {
                        emit sigFlowStatusReply(packet);
                    }else if(reply->name()==cmd::CmdStatusReply::name())
                    {
                        if(getCommunicateReply(packet)){
//                              communicateCmd.communicateCmdReply(packet);
//                            continue;
                        }
                        else
                            emit sigCmdStatusReply(packet);
                    }/*else if(reply->name()==cmd::GetAxisStatusReply::name())
                    {
                        emit sigGetAxisStatusReply(packet);
                    }else if(reply->name()==cmd::GetInportStatusReply::name())
                    {
                        emit sigGetInportStatusReply(packet);
                    }*/
                    else if(reply->name()==cmd::NodeStatusReply::name())
                    {
                        emit sigNodeStatusReply(packet);
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(15));
                    delete reply;
                    reply=nullptr;
                }
            }else{
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        }
}

bool TcpClient::getCommunicateReply(const QJsonObject &obj)
{
    if(obj.contains("CmdName")){
        QString cmdName=obj.value("CmdName").toString();
        if(cmdName==cmd::IODevAdd::name()
            ||cmdName==cmd::IODevSet::name()
            ||cmdName==cmd::IODevConnectHost::name()
            ||cmdName==cmd::IODevOpen::name()
            ||cmdName==cmd::IODevWrite::name()){
            return true;
        }
    }
    return false;
}
