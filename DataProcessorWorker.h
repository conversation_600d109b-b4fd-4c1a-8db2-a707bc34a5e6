#ifndef DATA_PROCESSOR_WORKER_H
#define DATA_PROCESSOR_WORKER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonDocument>
#include <QByteArray>
#include <QMutex>
#include <QAtomicInt>
#include <QUuid>
#include <QThread>

#include "utility/tinytextprocesser.h"
#include "cmd/factory.h"
#include "cmd/command.h"
#include "cmd/reply.h"
#include "cmd/cmdstatusreply.h"
#include "cmd/nodestatusreply.h"
#include "cmd/flowstatusreply.h"

/**
 * @brief 数据处理工作器类
 * 
 * 重构后的Ada3DataProcessor，使用moveToThread方式在独立线程中运行
 * 主要改进：
 * - 支持moveToThread模式
 * - 改进的异常处理机制，捕获所有类型的异常
 * - 使用原子操作确保线程安全
 * - 更好的资源管理和错误恢复
 * - 增强的日志记录和调试信息
 */
class DataProcessorWorker : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 处理错误类型
     */
    enum class ProcessError {
        NoError,
        DecompressionError,   // 解压缩错误
        JsonParseError,       // JSON解析错误
        InvalidMessageFormat, // 消息格式错误
        UnsupportedMessageType, // 不支持的消息类型
        CommandCreationError, // 命令创建错误
        InitializationError,  // 初始化错误
        UnknownError         // 未知错误
    };

    explicit DataProcessorWorker(QObject *parent = nullptr);
    ~DataProcessorWorker();

    /**
     * @brief 获取最后的处理错误
     * @return 错误类型
     */
    ProcessError lastError() const;

    /**
     * @brief 获取错误描述
     * @return 错误描述字符串
     */
    QString errorString() const;

    /**
     * @brief 检查是否已初始化
     * @return true如果已初始化
     */
    bool isInitialized() const;

public slots:
    /**
     * @brief 初始化数据处理器（在工作线程中调用）
     */
    void initialize();

    /**
     * @brief 清理数据处理器（在工作线程中调用）
     */
    void cleanup();

    /**
     * @brief 处理原始数据
     * @param rawData 原始二进制数据
     */
    void processRawData(const QByteArray &rawData);

    /**
     * @brief 编码JSON数据为二进制格式
     * @param json JSON对象
     */
    void encodeJsonData(const QJsonObject &json);

    /**
     * @brief 解码二进制数据为JSON对象
     * @param rawData 原始二进制数据
     */
    void decodeRawData(const QByteArray &rawData);

    /**
     * @brief 停止工作器（准备线程退出）
     */
    void stop();

signals:
    /**
     * @brief 初始化完成信号
     */
    void initialized();

    /**
     * @brief 清理完成信号
     */
    void cleanupCompleted();

    /**
     * @brief 命令接收信号
     * @param command 命令包装器
     * @param uuid 命令UUID
     */
    void commandReceived(const QJsonObject &command, const QUuid &uuid);

    /**
     * @brief 响应接收信号
     * @param reply 响应对象
     * @param uuid 响应UUID
     */
    void replyReceived(const QJsonObject &reply, const QUuid &uuid);

    /**
     * @brief JSON编码完成信号
     * @param encodedData 编码后的二进制数据
     * @param originalJson 原始JSON对象
     */
    void jsonEncoded(const QByteArray &encodedData, const QJsonObject &originalJson);

    /**
     * @brief 数据解码完成信号
     * @param decodedJson 解码后的JSON对象
     * @param originalData 原始二进制数据
     */
    void dataDecoded(const QJsonObject &decodedJson, const QByteArray &originalData);

    /**
     * @brief 处理错误信号
     * @param error 错误类型
     * @param errorString 错误描述
     */
    void processingError(ProcessError error, const QString &errorString);

    /**
     * @brief 编码错误信号
     * @param error 错误类型
     * @param errorString 错误描述
     * @param originalJson 导致错误的原始JSON
     */
    void encodingError(ProcessError error, const QString &errorString, const QJsonObject &originalJson);

    /**
     * @brief 解码错误信号
     * @param error 错误类型
     * @param errorString 错误描述
     * @param originalData 导致错误的原始数据
     */
    void decodingError(ProcessError error, const QString &errorString, const QByteArray &originalData);

private:
    /**
     * @brief 处理单个JSON消息
     * @param json JSON对象
     */
    void processSingleMessage(const QJsonObject &json);

    /**
     * @brief 识别消息类型
     * @param json JSON对象
     * @return 消息类型字符串
     */
    QString identifyMessageType(const QJsonObject &json);

    /**
     * @brief 提取UUID
     * @param json JSON对象
     * @return UUID
     */
    QUuid extractUuid(const QJsonObject &json);

    /**
     * @brief 设置错误状态（线程安全）
     * @param error 错误类型
     * @param errorString 错误描述
     */
    void setError(ProcessError error, const QString &errorString);

    /**
     * @brief 安全地执行操作，捕获所有异常
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @return true如果操作成功
     */
    bool safeExecute(std::function<void()> operation, const QString &operationName);

    /**
     * @brief 验证JSON对象的基本结构
     * @param json JSON对象
     * @return true如果结构有效
     */
    bool validateJsonStructure(const QJsonObject &json);

private:
    utl::TinyTextProcesser *m_textProcesser;  // ada3文本处理器

    mutable QMutex m_errorMutex;              // 错误状态保护
    ProcessError m_lastError;                 // 最后的错误
    QString m_errorString;                    // 错误描述

    QAtomicInt m_initialized;                 // 初始化状态（原子操作）
    QAtomicInt m_shouldStop;                  // 停止标志（原子操作）

    // 统计信息
    mutable QMutex m_statsMutex;
    qint64 m_messagesProcessed;               // 处理的消息数量
    qint64 m_errorsCount;                     // 错误计数
    qint64 m_startTime;                       // 启动时间
};

// 注册元类型以支持信号槽
Q_DECLARE_METATYPE(DataProcessorWorker::ProcessError)

#endif // DATA_PROCESSOR_WORKER_H
