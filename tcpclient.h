#ifndef TCPCLIENT_H
#define TCPCLIENT_H
#include <QObject>
#include <QMutex>
#include <thread>
#include <QQueue>
#include <QMessageBox>
#include "CommandTransceiverRefactored.h"
#include "network_global.h"

class NETWORK_EXPORT  :public QObject
{
    Q_OBJECT
public:
    TcpClient(const QString &appName,QObject*parent=nullptr);

    ~TcpClient();

    bool connectToServer(const QString &host, quint16 port, int timeoutMs = 5000);

    void disconnectFromServer();

    bool isConnected() const;

signals:
    void connectionStatusChanged(bool connected);
    void sigMsg(QMessageBox::Icon type,QString moduleName,QString msg,bool showMessageBox);
    void sigReplyFlowWriting(const QJsonObject&obj);
    void sigFlowStatusReply(const QJsonObject &obj);
    void sigCmdStatusReply(const QJsonObject&obj);
    void sigNodeStatusReply(const QJsonObject&obj);
    //  void sigGetAxisStatusReply(const QJsonObject &obj);
    // void sigGetInportStatusReply(const QJsonObject &obj);
    void sigReaderMsg(const QString &handle,const QByteArray &msg);

public slots:
    void writeTcpCfg(const QJsonObject&msg);
    void writeTcpAction(const QJsonObject&msg);

private slots:
    void onConnectionStateChanged(CommandTransceiverRefactored::ConnectionState state);

    void onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response);

    void packetProcessorThread();

private:
    void sendConfigThread();
    bool getCommunicateReply(const QJsonObject &obj);
    void processCachedCommands();
private:
    QQueue<QJsonObject>packetQueue;
    QQueue<QJsonObject>configQueue;
    QString m_appName;
    QMutex m_commandMutex;
    QMutex m_configMutex;
    bool bStart;
    QThread *packetThread;
    CommandTransceiverRefactored *m_commandTrans;
    mutable QMutex m_statusMutex;
};

#endif // TCPCLIENT_H
