#include "CommandTransceiverRefactored.h"
#include <QDebug>
#include <QMutexLocker>
#include <QApplication>
#include <QCoreApplication>
#include <QThread>

CommandTransceiverRefactored::CommandTransceiverRefactored(QObject *parent)
    : QObject(parent)
    , m_networkThread(nullptr)
    , m_dataProcessorThread(nullptr)
    , m_networkWorker(nullptr)
    , m_dataProcessorWorker(nullptr)
    , m_networkWorkerInitialized(0)
    , m_dataProcessorWorkerInitialized(0)
    , m_cleanupInProgress(0)
    , m_connectionState(ConnectionState::Disconnected)
    , m_pendingConnection(false)
    , m_syncTimeoutTimer(nullptr)
    , m_commandsSent(0)
    , m_responsesReceived(0)
    , m_errorsCount(0)
    , m_startTime(QDateTime::currentMSecsSinceEpoch())
{
    qDebug() << "CommandTransceiverRefactored: Created in thread" << QThread::currentThread();

    // 注册元类型（用于跨线程信号槽）- 必须在信号连接之前注册
//    static bool typesRegistered = false;
//    if (!typesRegistered) {
//        qDebug() << "CommandTransceiverRefactored: Registering meta types for cross-thread signals...";

//        qRegisterMetaType<NetworkWorker::ConnectionState>("NetworkWorker::ConnectionState");
//        qDebug() << "CommandTransceiverRefactored: Registered NetworkWorker::ConnectionState";

//        qRegisterMetaType<NetworkWorker::NetworkError>("NetworkWorker::NetworkError");
//        qDebug() << "CommandTransceiverRefactored: Registered NetworkWorker::NetworkError";

//        qRegisterMetaType<DataProcessorWorker::ProcessError>("DataProcessorWorker::ProcessError");
//        qDebug() << "CommandTransceiverRefactored: Registered DataProcessorWorker::ProcessError";

//        qRegisterMetaType<CommandTransceiverRefactored::CommandResponse>("CommandTransceiverRefactored::CommandResponse");
//        qDebug() << "CommandTransceiverRefactored: Registered CommandTransceiverRefactored::CommandResponse";

//        typesRegistered = true;
//        qDebug() << "CommandTransceiverRefactored: All meta types registered successfully!";
//    } else {
//        qDebug() << "CommandTransceiverRefactored: Meta types already registered, skipping";
//    }

    initializeComponents();
}

CommandTransceiverRefactored::~CommandTransceiverRefactored()
{
    qDebug() << "CommandTransceiverRefactored: Destructor called";
    cleanup();
}

void CommandTransceiverRefactored::initializeComponents()
{
    qDebug() << "CommandTransceiverRefactored: Initializing components";

    // 创建同步命令超时定时器
    m_syncTimeoutTimer = new QTimer(this);
    m_syncTimeoutTimer->setSingleShot(true);
    connect(m_syncTimeoutTimer, &QTimer::timeout, this, &CommandTransceiverRefactored::onSyncCommandTimeout);
    
    // 创建网络工作线程
    m_networkThread = new QThread(this);
    m_networkWorker = new NetworkWorker();
    m_networkWorker->moveToThread(m_networkThread);

    // 创建数据处理工作线程
    m_dataProcessorThread = new QThread(this);
    m_dataProcessorWorker = new DataProcessorWorker();
    m_dataProcessorWorker->moveToThread(m_dataProcessorThread);

    
    // 确保线程启动后再初始化组件
    connect(m_networkThread, &QThread::started, this, [this](){
        QMetaObject::invokeMethod(m_networkWorker, "initialize", Qt::QueuedConnection);
    });

    connect(m_dataProcessorThread, &QThread::started, this, [this](){
        QMetaObject::invokeMethod(m_dataProcessorWorker, "initialize", Qt::QueuedConnection);
    });

    connect(m_networkThread, &QThread::finished, m_networkWorker, &QObject::deleteLater);
    connect(m_dataProcessorThread, &QThread::finished, m_dataProcessorWorker, &QObject::deleteLater);

    // 连接信号槽
    connectSignals();

    // 启动工作线程
    m_networkThread->start();
    m_dataProcessorThread->start();

    
    qDebug() << "CommandTransceiverRefactored: Component initialization started";
}

void CommandTransceiverRefactored::connectSignals()
{
    // 确保元类型已注册（双重保险）
//    static bool signalTypesRegistered = false;
//    if (!signalTypesRegistered) {
//        qDebug() << "CommandTransceiverRefactored::connectSignals: Ensuring meta types are registered...";
//        qRegisterMetaType<NetworkWorker::ConnectionState>("NetworkWorker::ConnectionState");
//        qRegisterMetaType<NetworkWorker::NetworkError>("NetworkWorker::NetworkError");
//        qRegisterMetaType<DataProcessorWorker::ProcessError>("DataProcessorWorker::ProcessError");
//        signalTypesRegistered = true;
//        qDebug() << "CommandTransceiverRefactored::connectSignals: Meta types registration completed";
//    }

    // 网络Worker信号连接
    connect(m_networkWorker, &NetworkWorker::initialized,
            this, &CommandTransceiverRefactored::onNetworkWorkerInitialized);
    connect(m_networkWorker, &NetworkWorker::cleanupCompleted,
            this, &CommandTransceiverRefactored::onNetworkWorkerCleanupCompleted);
    connect(m_networkWorker, &NetworkWorker::connectionStateChanged,
            this, &CommandTransceiverRefactored::onNetworkConnectionStateChanged);
    connect(m_networkWorker, &NetworkWorker::rawDataReceived,
            this, &CommandTransceiverRefactored::onNetworkRawDataReceived);
    connect(m_networkWorker, &NetworkWorker::networkError,
            this, &CommandTransceiverRefactored::onNetworkError);
    connect(m_networkWorker, &NetworkWorker::dataSent,
            this, &CommandTransceiverRefactored::onNetworkDataSent);
    connect(m_networkWorker, &NetworkWorker::dataSendFailed,
            this, &CommandTransceiverRefactored::onNetworkDataSendFailed);
    
    // 数据处理Worker信号连接
    connect(m_dataProcessorWorker, &DataProcessorWorker::initialized,
            this, &CommandTransceiverRefactored::onDataProcessorWorkerInitialized);
    connect(m_dataProcessorWorker, &DataProcessorWorker::cleanupCompleted,
            this, &CommandTransceiverRefactored::onDataProcessorWorkerCleanupCompleted);
    connect(m_dataProcessorWorker, &DataProcessorWorker::commandReceived,
            this, &CommandTransceiverRefactored::onDataProcessorCommandReceived);
    connect(m_dataProcessorWorker, &DataProcessorWorker::replyReceived,
            this, &CommandTransceiverRefactored::onDataProcessorReplyReceived);
    connect(m_dataProcessorWorker, &DataProcessorWorker::jsonEncoded,
            this, &CommandTransceiverRefactored::onDataProcessorJsonEncoded);
    connect(m_dataProcessorWorker, &DataProcessorWorker::processingError,
            this, &CommandTransceiverRefactored::onDataProcessorError);
    
    qDebug() << "CommandTransceiverRefactored: Signals connected";
}

void CommandTransceiverRefactored::cleanup()
{
    qDebug() << "CommandTransceiverRefactored: Shutting down threads and cleaning up";

    if (m_networkWorker) {
        QMetaObject::invokeMethod(m_networkWorker, "stop", Qt::QueuedConnection);
        QMetaObject::invokeMethod(m_networkWorker, "cleanup", Qt::QueuedConnection);
    }
    if (m_dataProcessorWorker) {
        QMetaObject::invokeMethod(m_dataProcessorWorker, "stop", Qt::QueuedConnection);
        QMetaObject::invokeMethod(m_dataProcessorWorker, "cleanup", Qt::QueuedConnection);
    }

    if (m_networkThread) {
        m_networkThread->quit();
        m_networkThread->wait();  // 会自动触发 deleteLater
    }
    if (m_dataProcessorThread) {
        m_dataProcessorThread->quit();
        m_dataProcessorThread->wait();
    }

    m_networkWorker = nullptr;
    m_dataProcessorWorker = nullptr;

    // 清理同步命令
    cleanupTimeoutSyncCommands();

    qDebug() << "CommandTransceiverRefactored: Cleanup completed";

}

bool CommandTransceiverRefactored::connectToServer(const ConnectionConfig &config)
{
    qDebug() << "CommandTransceiverRefactored: Connect to server" << config.serverAddress.toString() << ":" << config.serverPort;

    // 如果还没有初始化完成，延迟连接
    if (!isInitialized()) {
        qDebug() << "CommandTransceiverRefactored: Workers not yet initialized, delaying connection";

        // 保存配置，等待初始化完成后自动连接
        {
            QMutexLocker locker(&m_connectionMutex);
            m_connectionConfig = config;
            m_pendingConnection = true;
        }

        return true; // 返回true表示连接请求已接受，将在初始化完成后执行
    }

    return performConnection(config);
}

bool CommandTransceiverRefactored::connectToServer(const QString &host, quint16 port)
{
    ConnectionConfig config;
    config.serverAddress = QHostAddress(host);
    config.serverPort = port;
    return connectToServer(config);
}

void CommandTransceiverRefactored::disconnectFromServer()
{
    qDebug() << "CommandTransceiverRefactored: Disconnect from server";
    
    if (m_networkWorker) {
        QMetaObject::invokeMethod(m_networkWorker, "disconnectFromServer", Qt::QueuedConnection);
    }
}

CommandTransceiverRefactored::ConnectionState CommandTransceiverRefactored::getConnectionState() const
{
    QMutexLocker locker(&m_connectionMutex);
    return m_connectionState;
}

bool CommandTransceiverRefactored::isConnected() const
{
    return getConnectionState() == ConnectionState::Connected;
}

bool CommandTransceiverRefactored::isInitialized() const
{
    return m_networkWorkerInitialized.loadRelaxed() && m_dataProcessorWorkerInitialized.loadRelaxed();
}

QUuid CommandTransceiverRefactored::sendCommand(const QJsonObject &command, const QUuid &uuid)
{
    QUuid commandUuid = uuid.isNull() ? generateCommandUuid() : uuid;
    
    if (!isConnected()) {
        CommandResponse response;
        response.result = CommandResult::NotConnected;
        response.errorMessage = "Not connected to server";
        response.commandUuid = commandUuid;
        
        emit commandResponseReceived(response);
        return commandUuid;
    }
    
    // 添加UUID到命令中
    QJsonObject commandWithUuid = command;
    commandWithUuid["Uuid"] = commandUuid.toString();
    
    // 编码并发送
    if (m_dataProcessorWorker) {
        QMetaObject::invokeMethod(m_dataProcessorWorker, "encodeJsonData", Qt::QueuedConnection,
                                 Q_ARG(QJsonObject, commandWithUuid));
    }
    
    // 更新统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_commandsSent++;
    }
    
    return commandUuid;
}

QUuid CommandTransceiverRefactored::sendCommand(const QJsonObject &command, 
                                               std::function<void(const CommandResponse&)> callback,
                                               const QUuid &uuid)
{
    QUuid commandUuid = uuid.isNull() ? generateCommandUuid() : uuid;
    
    // 保存回调
    {
        QMutexLocker locker(&m_callbackMutex);
        m_commandCallbacks[commandUuid] = callback;
    }
    
    // 发送命令
    sendCommand(command, commandUuid);
    
    return commandUuid;
}

CommandTransceiverRefactored::CommandResponse CommandTransceiverRefactored::sendCommandSync(
    const QJsonObject &command, int timeoutMs, const QUuid &uuid)
{
    QUuid commandUuid = uuid.isNull() ? generateCommandUuid() : uuid;
    
    CommandResponse response;
    response.commandUuid = commandUuid;
    
    if (!isConnected()) {
        response.result = CommandResult::NotConnected;
        response.errorMessage = "Not connected to server";
        return response;
    }
    
    // 设置超时
    int actualTimeout = (timeoutMs > 0) ? timeoutMs : m_connectionConfig.commandTimeoutMs;
    
    // 创建等待条件
    QWaitCondition *waitCondition = new QWaitCondition();
    
    {
        QMutexLocker locker(&m_syncCommandMutex);
        m_pendingSyncCommands[commandUuid] = &response;
        m_syncCommandConditions[commandUuid] = waitCondition;
    }
    
    // 发送命令
    sendCommand(command, commandUuid);
    
    // 等待响应
    {
        QMutexLocker locker(&m_syncCommandMutex);
        bool success = waitCondition->wait(&m_syncCommandMutex, actualTimeout);
        
        // 清理
        m_pendingSyncCommands.remove(commandUuid);
        m_syncCommandConditions.remove(commandUuid);
        
        if (!success) {
            response.result = CommandResult::Timeout;
            response.errorMessage = "Command timeout";
        }
    }
    
    delete waitCondition;
    return response;
}

// ========== 槽函数实现 ==========

void CommandTransceiverRefactored::onNetworkWorkerInitialized()
{
    qDebug() << "CommandTransceiverRefactored: Network worker initialized";
    m_networkWorkerInitialized.storeRelaxed(1);
    checkAndEmitInitialized();
}

void CommandTransceiverRefactored::onDataProcessorWorkerInitialized()
{
    qDebug() << "CommandTransceiverRefactored: Data processor worker initialized";
    m_dataProcessorWorkerInitialized.storeRelaxed(1);
    checkAndEmitInitialized();
}

void CommandTransceiverRefactored::onNetworkWorkerCleanupCompleted()
{
    qDebug() << "CommandTransceiverRefactored: Network worker cleanup completed";
}

void CommandTransceiverRefactored::onDataProcessorWorkerCleanupCompleted()
{
    qDebug() << "CommandTransceiverRefactored: Data processor worker cleanup completed";
}

void CommandTransceiverRefactored::onNetworkConnectionStateChanged(NetworkWorker::ConnectionState state)
{
    bool stateChanged = false;
    {
        QMutexLocker locker(&m_connectionMutex);
        if (m_connectionState != state) {
            m_connectionState = state;
            stateChanged = true;
        }
    }

    if (stateChanged) {
        qDebug() << "CommandTransceiverRefactored: Connection state changed to" << static_cast<int>(state);
        emit connectionStateChanged(state);
    }
}

void CommandTransceiverRefactored::onNetworkRawDataReceived(const QByteArray &data)
{
    // 将原始数据传递给数据处理器
    if (m_dataProcessorWorker) {
        QMetaObject::invokeMethod(m_dataProcessorWorker, "processRawData", Qt::QueuedConnection,
                                 Q_ARG(QByteArray, data));
    }
}

void CommandTransceiverRefactored::onNetworkError(NetworkWorker::NetworkError error, const QString &description)
{
    QString errorMsg = QString("Network error (%1): %2").arg(static_cast<int>(error)).arg(description);
    qWarning() << "CommandTransceiverRefactored:" << errorMsg;

    {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }

    emit errorOccurred(errorMsg);
}

void CommandTransceiverRefactored::onNetworkDataSent(qint64 bytesWritten)
{
    Q_UNUSED(bytesWritten)
    // 可以在这里添加发送统计
}

void CommandTransceiverRefactored::onNetworkDataSendFailed(const QString &error)
{
    qWarning() << "CommandTransceiverRefactored: Data send failed:" << error;

    {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }

    emit errorOccurred(QString("Data send failed: %1").arg(error));
}

void CommandTransceiverRefactored::onDataProcessorCommandReceived(const QJsonObject &command, const QUuid &uuid)
{
    qDebug() << "CommandTransceiverRefactored: Command received, UUID:" << uuid.toString();
    emit commandReceived(command, uuid);
}

void CommandTransceiverRefactored::onDataProcessorReplyReceived(const QJsonObject &reply, const QUuid &uuid)
{
    qDebug() << "CommandTransceiverRefactored: Reply received, UUID:" << uuid.toString();

    // 检查是否是同步命令的响应
    {
        QMutexLocker locker(&m_syncCommandMutex);
        if (m_pendingSyncCommands.contains(uuid)) {
            CommandResponse *response = m_pendingSyncCommands[uuid];
            response->result = CommandResult::Success;
            response->data = reply;
            response->responseTime = QDateTime::currentMSecsSinceEpoch() - response->createTime;

            QWaitCondition *condition = m_syncCommandConditions.value(uuid);
            if (condition) {
                condition->wakeOne();
            }

            {
                QMutexLocker statsLocker(&m_statsMutex);
                m_responsesReceived++;
            }

            return;
        }
    }

    // 检查是否有异步回调
    {
        QMutexLocker locker(&m_callbackMutex);
        if (m_commandCallbacks.contains(uuid)) {
            auto callback = m_commandCallbacks.take(uuid);

            CommandResponse response;
            response.result = CommandResult::Success;
            response.data = reply;
            response.commandUuid = uuid;
            response.responseTime = QDateTime::currentMSecsSinceEpoch() - response.createTime;

            callback(response);

            {
                QMutexLocker statsLocker(&m_statsMutex);
                m_responsesReceived++;
            }

            return;
        }
    }

    // 发送通用响应信号
    emit replyReceived(reply, uuid);

    {
        QMutexLocker locker(&m_statsMutex);
        m_responsesReceived++;
    }
}

void CommandTransceiverRefactored::onDataProcessorJsonEncoded(const QByteArray &encodedData, const QJsonObject &originalJson)
{
    Q_UNUSED(originalJson)

    // 发送编码后的数据
    if (m_networkWorker) {
        QMetaObject::invokeMethod(m_networkWorker, "sendData", Qt::QueuedConnection,
                                 Q_ARG(QByteArray, encodedData));
    }
}

void CommandTransceiverRefactored::onDataProcessorError(DataProcessorWorker::ProcessError error, const QString &errorString)
{
    QString errorMsg = QString("Data processing error (%1): %2").arg(static_cast<int>(error)).arg(errorString);
    qWarning() << "CommandTransceiverRefactored:" << errorMsg;

    {
        QMutexLocker locker(&m_statsMutex);
        m_errorsCount++;
    }

    emit errorOccurred(errorMsg);
}

void CommandTransceiverRefactored::onSyncCommandTimeout()
{
    cleanupTimeoutSyncCommands();
}

// ========== 辅助方法实现 ==========

QUuid CommandTransceiverRefactored::generateCommandUuid()
{
    return QUuid::createUuid();
}

void CommandTransceiverRefactored::handleCommandResponse(const QUuid &uuid, const CommandResponse &response)
{
    // 检查同步命令
    {
        QMutexLocker locker(&m_syncCommandMutex);
        if (m_pendingSyncCommands.contains(uuid)) {
            CommandResponse *syncResponse = m_pendingSyncCommands[uuid];
            *syncResponse = response;

            QWaitCondition *condition = m_syncCommandConditions.value(uuid);
            if (condition) {
                condition->wakeOne();
            }
            return;
        }
    }

    // 检查异步回调
    {
        QMutexLocker locker(&m_callbackMutex);
        if (m_commandCallbacks.contains(uuid)) {
            auto callback = m_commandCallbacks.take(uuid);
            callback(response);
            return;
        }
    }

    // 发送通用信号
    emit commandResponseReceived(response);
}

void CommandTransceiverRefactored::cleanupTimeoutSyncCommands()
{
    QMutexLocker locker(&m_syncCommandMutex);

    //qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    QList<QUuid> timeoutCommands;

    for (auto it = m_pendingSyncCommands.begin(); it != m_pendingSyncCommands.end(); ++it) {
        CommandResponse *response = it.value();
        if (response->isTimeout(m_connectionConfig.commandTimeoutMs)) {
            timeoutCommands.append(it.key());

            response->result = CommandResult::Timeout;
            response->errorMessage = "Command timeout";

            QWaitCondition *condition = m_syncCommandConditions.value(it.key());
            if (condition) {
                condition->wakeOne();
            }
        }
    }

    qDebug() << "CommandTransceiverRefactored: Cleaned up" << timeoutCommands.size() << "timeout commands";
}

void CommandTransceiverRefactored::checkAndEmitInitialized()
{
    // 检查是否所有Worker都已初始化
    if (m_networkWorkerInitialized.loadRelaxed() && m_dataProcessorWorkerInitialized.loadRelaxed()) {
        qDebug() << "CommandTransceiverRefactored: All workers initialized, emitting initialized signal";
        emit initialized();

        // 如果有待处理的连接请求，现在执行它
        bool shouldConnect = false;
        ConnectionConfig config;
        {
            QMutexLocker locker(&m_connectionMutex);
            if (m_pendingConnection) {
                shouldConnect = true;
                config = m_connectionConfig;
                m_pendingConnection = false;
            }
        }

        if (shouldConnect) {
            qDebug() << "CommandTransceiverRefactored: Executing pending connection request";
            performConnection(config);
        }
    }
}

bool CommandTransceiverRefactored::performConnection(const ConnectionConfig &config)
{
    qDebug() << "CommandTransceiverRefactored: Performing connection to" << config.serverAddress.toString() << ":" << config.serverPort;

    // 保存配置
    {
        QMutexLocker locker(&m_connectionMutex);
        m_connectionConfig = config;
    }

    // 配置网络Worker
    QMetaObject::invokeMethod(m_networkWorker, "setAutoReconnect", Qt::QueuedConnection,
                             Q_ARG(bool, config.autoReconnect),
                             Q_ARG(int, config.reconnectIntervalMs));

    QMetaObject::invokeMethod(m_networkWorker, "setHeartbeat", Qt::QueuedConnection,
                             Q_ARG(bool, config.enableHeartbeat),
                             Q_ARG(int, config.heartbeatIntervalMs));

    // 发起连接
    QMetaObject::invokeMethod(m_networkWorker, "connectToServer", Qt::QueuedConnection,
                             Q_ARG(QString, config.serverAddress.toString()),
                             Q_ARG(quint16, config.serverPort),
                             Q_ARG(int, config.connectTimeoutMs));

    return true;
}

// ========== 配置方法实现 ==========

void CommandTransceiverRefactored::setConnectionConfig(const ConnectionConfig &config)
{
    QMutexLocker locker(&m_connectionMutex);
    m_connectionConfig = config;
}

CommandTransceiverRefactored::ConnectionConfig CommandTransceiverRefactored::getConnectionConfig() const
{
    QMutexLocker locker(&m_connectionMutex);
    return m_connectionConfig;
}

QJsonObject CommandTransceiverRefactored::getStatistics() const
{
    QMutexLocker locker(&m_statsMutex);

    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    qint64 uptime = currentTime - m_startTime;

    QJsonObject stats;
    stats["commandsSent"] = m_commandsSent;
    stats["responsesReceived"] = m_responsesReceived;
    stats["errorsCount"] = m_errorsCount;
    stats["uptime"] = uptime;
    stats["startTime"] = m_startTime;

    // 计算速率（每秒）
    if (uptime > 0) {
        stats["commandsPerSecond"] = (m_commandsSent * 1000.0) / uptime;
        stats["responsesPerSecond"] = (m_responsesReceived * 1000.0) / uptime;
        stats["errorsPerSecond"] = (m_errorsCount * 1000.0) / uptime;
    }

    return stats;
}


