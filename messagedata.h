﻿#ifndef MESSAGEDATA_H
#define MESSAGEDATA_H

#include <QObject>
#include <QMessageBox>
#include <QLabel>
#include <QTimer>
#include <mutex>
#include <memory>

enum MsgModel{
    MessageBox=1<<0,
    Log=1<<1,
    Console=1<<2,
    Widget=1<<3
};

class MessageDataPrivate;

class  MessageData : public QObject
{
    Q_OBJECT
public:
    explicit MessageData(QObject *parent = nullptr);
    void setWidget(QLabel*label);           //设置信息显示的label
    void setLogPath(QString logPath);       //设置日志路径
    void setMsgShowModel(int msgModel);     //设置信息显示模式

public slots:
    void receiveMsg(QMessageBox::Icon level,QString moduleName,QString info,bool messageBox=false);

private slots:
        void showLabelMsg(int flashNumber,QColor bgColor,const QString &info);
private:
    QLabel *labelMessage=nullptr;
    void showMsgInfo();

    struct _msg{
        int msgModel;
        QMessageBox::Icon level;
        QString moduleName;
        QString info;
    };
    int m_msgModel=0;
    std::vector<_msg>m_msg;
    QString m_logPath;
    QFont m_font;
    QColor m_originColor;
    int maxFlashes;
    int flashCount;
    QString m_info;
    QColor m_color;
    QString whiteColorBg;
    QString flashColor;
    QTimer *flashTimer=nullptr;

};

#endif // MESSAGEDATA_H
