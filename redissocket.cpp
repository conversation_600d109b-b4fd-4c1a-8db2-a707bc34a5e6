#include "redissocket.h"
#include "stater/redisstatuser.h"

RedisSocket::RedisSocket(const QString appName, QObject *parent)
    :QObject(parent),
    m_appName(appName),
    m_statuser(nullptr),
    m_connected(false),
    m_statuThread(nullptr),
    m_interval(3000)
{
    m_monitorTimer=new QTimer();
    m_monitorTimer->setInterval(m_interval);
    connect(m_monitorTimer,&QTimer::timeout,this,&RedisSocket::checkConnectionStatus);

    m_statuThread = new QThread(this);
    m_monitorTimer->moveToThread(m_statuThread);
    m_statuThread->start();
}

RedisSocket::~RedisSocket()
{
    QMetaObject::invokeMethod(m_monitorTimer, "stop");
    if(m_statuser){
        m_statuser->disConnect();
        delete m_statuser;
        m_statuser=nullptr;
    }
    if (m_statuThread && m_statuThread->isRunning()) {
        m_statuThread->quit();
        m_statuThread->wait();
    }
}

bool RedisSocket::connectRedis(QString hostname, int port, int timeout)
{
    if(!m_statuser){
        cmd::ADMInfo info(m_appName,cmd::ADMInfo::Type::Basket,m_appName,m_appName);
        m_statuser=new sttr::RedisStatuser(info,hostname,port,timeout);
        initRedisSig();
        m_connected=m_statuser->reset();
        QMetaObject::invokeMethod(m_monitorTimer, "start");
    }else{
        sttr::MSStatuser *s=static_cast<sttr::MSStatuser*>(m_statuser);
        if(s->host()!=hostname||s->port()!=port){
            s->setHost(hostname);
            s->setPort(port);
            m_statuser->disConnect();
            m_connected=false;
            m_connected=m_statuser->reset();
        }
    }
    if(m_connected)
        emit sigMsg(QMessageBox::Information,tr("Redis"),tr("Redis connection successful."),false);
    else
        emit sigMsg(QMessageBox::Information,tr("Redis"),tr("Redis connection failed"),false);
    return m_connected;
}

void RedisSocket::disConnectRedis()
{
    QMutexLocker lock(&m_mutex);
    if(m_statuser&&m_connected){
        m_statuser->disconnect();
    }
}

bool RedisSocket::isConnected() const
{
    QMutexLocker lock(&m_mutex);
    return m_connected;
}

void RedisSocket::setReconnectInterval(int msec)
{
    QMutexLocker lock(&m_mutex);
    if(msec){
        m_interval=msec;
        QMetaObject::invokeMethod (m_monitorTimer, "setInterval", Q_ARG (int, m_interval));
    }
}

void RedisSocket::varAddedSlots(const QUuid &uuid, const cmd::Var &var, const QString &label)
{
    emit varAdded(uuid,var,label);
}

void RedisSocket::varChangedSlots(const QUuid &uuid, const cmd::Var &var, const QString &label)
{
    emit varChanged(uuid,var,label);
}

void RedisSocket::varRemovedSlots(const QUuid &uuid)
{
    emit varRemoved(uuid);
}

void RedisSocket::iodevReadSlots(const QString &handle, const QByteArray &msg)
{
    emit iodevRead(handle,msg);
}

void RedisSocket::iodevAddedSlots(const QString &handle, cmd::DevState state)
{
    emit iodevAdded(handle,state);
}

void RedisSocket::iodevStateChangedSlots(const QString &handle, cmd::DevState state)
{
    emit iodevStateChanged(handle,state);
}

void RedisSocket::iodevRemovedSlots(const QString &handle)
{
    emit iodevRemoved(handle);
}

void RedisSocket::modbusCliStateChangedSlots(const QString &handle, cmd::com::State newState)
{
    emit modbusCliStateChanged(handle,newState);
}

void RedisSocket::modbusCliStateRemovedSlots(const QString &handle)
{
    emit modbusCliStateRemoved(handle);
}

void RedisSocket::checkConnectionStatus()
{
    QMutexLocker locker(&m_mutex);
    if (m_statuser && !m_connected) {
        sttr::MSStatuser *s=static_cast<sttr::MSStatuser*>(m_statuser);
        if(!s->host().isEmpty()&&s->port()>0){
            emit sigMsg(QMessageBox::Information,tr("Redis"),tr("Reconnecting to Redis...")+s->host()+QString::number(s->port()),false);
            m_connected=m_statuser->reset();
            if(!m_connected){
                emit sigMsg(QMessageBox::Information,tr("Redis"),tr("Redis connection restored"),false);
            }else{
                emit sigMsg(QMessageBox::Information,tr("Redis"),tr("Reconnect failed, will try again later"),false);
            }
        }
    }
}

void RedisSocket::initRedisSig()
{
    connect(m_statuser,SIGNAL(varAdded(QUuid,cmd::Var, QString)),this,SLOT(varAddedSlots(QUuid,cmd::Var,QString)));
    connect(m_statuser,SIGNAL(varChanged(QUuid, cmd::Var,QString)),this,SLOT(varChangedSlots(QUuid,cmd::Var,QString)));
    connect(m_statuser,SIGNAL(varRemoved(QUuid)),this,SLOT(varRemovedSlots(QUuid)));
    connect(m_statuser,SIGNAL(iodevRead(QString,QByteArray)),this,SLOT(iodevReadSlots(QString,QByteArray)));
    connect(m_statuser,SIGNAL(iodevAdded(QString, cmd::DevState)),this,SLOT(iodevAddedSlots(QString,cmd::DevState)));
    connect(m_statuser,SIGNAL(iodevStateChanged(QString,cmd::DevState)),this,SLOT(iodevStateChangedSlots(QString,cmd::DevState)));
    connect(m_statuser,SIGNAL(iodevRemoved(QString)),this,SLOT(iodevRemovedSlots(QString)));
    connect(m_statuser,SIGNAL(modbusCliStateChanged(QString,cmd::com::State)),this,SLOT(modbusCliStateChangedSlots(QString,cmd::com::State)));
    connect(m_statuser,SIGNAL(modbusCliRemoved(QString)),this,SLOT(modbusCliStateRemovedSlots(QString)));
}
