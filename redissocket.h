﻿#ifndef REDISSOCKET_H
#define REDISSOCKET_H
#include "stater/statuser.h"
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QMessageBox>
#include "network_global.h"

class NETWORK_EXPORT RedisSocket:public QObject
{
    Q_OBJECT
public:
    RedisSocket(const QString appName,QObject *parent=nullptr);

    ~RedisSocket();

    bool connectRedis(QString hostname,int port=6379,int timeout = 5000) ;

    void disConnectRedis();

    bool isConnected() const;

    void setReconnectInterval(int msec);

signals:
    void sigMsg(QMessageBox::Icon type,QString moduleName,QString msg,bool showMessageBox);

    void varAdded(const QUuid &uuid, const cmd::Var &var, const QString &label);
    void varChanged(const QUuid &uuid, const cmd::Var &var, const QString &label);
    void varRemoved(const QUuid &uuid);

    void iodevRead(const QString &handle, const QByteArray &msg);

    void iodevAdded(const QString &handle, cmd::DevState state);
    void iodevStateChanged(const QString &handle, cmd::DevState state);
    void iodevRemoved(const QString &handle);

    void modbusCliStateChanged(const QString &handle, cmd::com::State newState);
    void modbusCliStateRemoved(const QString &handle);

private slots:
    void varAddedSlots(const QUuid &uuid, const cmd::Var &var, const QString &label);
    void varChangedSlots(const QUuid &uuid, const cmd::Var &var, const QString &label);
    void varRemovedSlots(const QUuid &uuid);

    void iodevReadSlots(const QString &handle, const QByteArray &msg);

    void iodevAddedSlots(const QString &handle, cmd::DevState state);
    void iodevStateChangedSlots(const QString &handle, cmd::DevState state);
    void iodevRemovedSlots(const QString &handle);

    void modbusCliStateChangedSlots(const QString &handle, cmd::com::State newState);
    void modbusCliStateRemovedSlots(const QString &handle);

    void checkConnectionStatus();

private:
    void initRedisSig();

private:
    sttr::Statuser *m_statuser;
    bool m_connected;
    QThread *m_statuThread;
    QTimer *m_monitorTimer;
    mutable QMutex m_mutex;
    int m_interval;
    QString m_appName;
};

#endif // REDISSOCKET_H
