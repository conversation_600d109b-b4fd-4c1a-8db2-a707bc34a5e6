#include "NetworkWorker.h"
#include <QDebug>
#include <QMutexLocker>
#include <QJsonObject>
#include <QJsonDocument>
#include <QDateTime>
#include <QThread>

NetworkWorker::NetworkWorker(QObject *parent)
    : QObject(parent)
    , m_socket(nullptr)
    , m_rwsStrategy(nullptr)  // 读写策略实例
    , m_rwsConfig(RWSConfig::createDefault())  // 默认使用PreLength策略保持兼容性
    , m_host()
    , m_port(0)
    , m_connectionTimeout(5000)
    , m_connectionState(NetworkWorker::ConnectionState::Disconnected)
    , m_shouldStop(0)  // 原子操作初始化
    , m_reconnectTimer(nullptr)
    , m_autoReconnectEnabled(0)  // 原子操作初始化
    , m_reconnectInterval(3000)
    , m_heartbeatTimer(nullptr)
    , m_heartbeatEnabled(0)  // 原子操作初始化
    , m_heartbeatInterval(30000)
    , m_connectionTimeoutTimer(nullptr)
{
    qDebug() << "NetworkWorker: Created in thread" << QThread::currentThread();
    qRegisterMetaType<NetworkWorker::ConnectionState>("NetworkWorker::ConnectionState");
    qRegisterMetaType<NetworkWorker::NetworkError>("NetworkWorker::NetworkError");
}

NetworkWorker::~NetworkWorker()
{
    qDebug() << "NetworkWorker: Destructor called in thread" << QThread::currentThread();
    // 确保清理工作在正确的线程中完成
    if (m_socket || m_rwsStrategy || m_reconnectTimer || m_heartbeatTimer || m_connectionTimeoutTimer) {
        qWarning() << "NetworkWorker: Destructor called before cleanup() - this may cause issues";
    }
}

void NetworkWorker::initialize()
{
    qDebug() << "NetworkWorker: Initializing in thread" << QThread::currentThread();

    // 创建Socket
    m_socket = new QTcpSocket(this);

    // 创建读写策略实例
    m_rwsStrategy = createRWSStrategy(m_rwsConfig);
    if (!m_rwsStrategy) {
        qCritical() << "NetworkWorker: Failed to create RWS strategy, using default PreLength strategy";
        // 如果创建失败，强制使用PreLength策略
        RWSConfig defaultConfig = RWSConfig::createDefault();
        m_rwsStrategy = RWSStrategyFactory::createStrategy(defaultConfig);
        if (!m_rwsStrategy) {
            qFatal("NetworkWorker: Failed to create default RWS strategy - this should never happen");
        }
    }

    qDebug() << "NetworkWorker: Using RWS strategy:" << m_rwsStrategy->strategyName();

    // 连接Socket信号
    connect(m_socket, &QTcpSocket::connected, this, &NetworkWorker::onSocketConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &NetworkWorker::onSocketDisconnected);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &NetworkWorker::onSocketError);
    connect(m_socket, &QTcpSocket::readyRead, this, &NetworkWorker::onSocketReadyRead);
    connect(m_socket, &QTcpSocket::bytesWritten, this, &NetworkWorker::onSocketBytesWritten);
    
    // 创建定时器
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &NetworkWorker::onReconnectTimer);
    
    m_heartbeatTimer = new QTimer(this);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &NetworkWorker::onHeartbeatTimer);
    
    m_connectionTimeoutTimer = new QTimer(this);
    m_connectionTimeoutTimer->setSingleShot(true);
    connect(m_connectionTimeoutTimer, &QTimer::timeout, this, &NetworkWorker::onConnectionTimeout);
    
    qDebug() << "NetworkWorker: Initialization completed";
    emit initialized();
}

void NetworkWorker::cleanup()
{
    qDebug() << "NetworkWorker: Cleanup started in thread" << QThread::currentThread();
    
    // 设置停止标志
    m_shouldStop.storeRelaxed(1);
    
    // 停止所有定时器
    safeCleanupTimer(m_connectionTimeoutTimer);
    safeCleanupTimer(m_heartbeatTimer);
    safeCleanupTimer(m_reconnectTimer);
    
    // 清理Socket
    if (m_socket) {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(1000);
        }
        m_socket->deleteLater();
        m_socket = nullptr;
    }
    
    // 清理读写策略实例
    if (m_rwsStrategy) {
        // 清空策略缓冲区，避免数据丢失
        m_rwsStrategy->clearBuffer();
        m_rwsStrategy.reset();  // 释放unique_ptr
    }
    
    qDebug() << "NetworkWorker: Cleanup completed";
    emit cleanupCompleted();
}

void NetworkWorker::connectToServer(const QString &host, quint16 port, int timeoutMs)
{
    qDebug() << "NetworkWorker: Connect request - host:" << host << "port:" << port << "timeout:" << timeoutMs;
    
    if (m_shouldStop.loadRelaxed()) {
        qDebug() << "NetworkWorker: Ignoring connect request - worker is stopping";
        return;
    }
    
    if (!m_socket) {
        emit networkError(NetworkError::SocketError, "Socket not initialized");
        return;
    }
    
    // 保存连接参数
    m_host = host;
    m_port = port;
    m_connectionTimeout = timeoutMs;
    
    // 检查当前状态
    {
        QMutexLocker locker(&m_stateMutex);
        if (m_connectionState == ConnectionState::Connected) {
            qDebug() << "NetworkWorker: Already connected";
            return;
        }
    }
    
    // 如果正在连接，先断开
    if (m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
        m_socket->waitForDisconnected(1000);
    }
    
    // 开始连接
    setConnectionState(ConnectionState::Connecting);
    
    // 启动连接超时定时器
    if (m_connectionTimeoutTimer) {
        m_connectionTimeoutTimer->start(timeoutMs);
    }
    
    // 发起连接
    m_socket->connectToHost(host, port);
    qDebug() << "NetworkWorker: Connection initiated";
}

void NetworkWorker::disconnectFromServer()
{
    qDebug() << "NetworkWorker: Disconnect requested";
    
    // 停止自动重连
    m_autoReconnectEnabled.storeRelaxed(0);
    stopReconnectTimer();
    
    // 停止连接超时定时器
    if (m_connectionTimeoutTimer) {
        m_connectionTimeoutTimer->stop();
    }
    
    // 断开连接
    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
    } else {
        setConnectionState(ConnectionState::Disconnected);
    }
}

void NetworkWorker::sendData(const QByteArray &data)
{
    if (m_shouldStop.loadRelaxed()) {
        emit dataSendFailed("Worker is stopping");
        return;
    }
    
    if (!m_socket || m_socket->state() != QAbstractSocket::ConnectedState) {
        emit dataSendFailed("Socket not connected");
        return;
    }
    
    if (!m_rwsStrategy) {
        emit dataSendFailed("RWS strategy not available");
        return;
    }

    if (data.isEmpty()) {
        emit dataSendFailed("Empty data");
        return;
    }

    try {
        // 使用当前读写策略封装消息
        QByteArray wrappedData = m_rwsStrategy->wrapMessage(data);
        qint64 bytesWritten = m_socket->write(wrappedData);
        
        if (bytesWritten == -1) {
            emit dataSendFailed(QString("Write failed: %1").arg(m_socket->errorString()));
        } else if (bytesWritten != wrappedData.size()) {
            emit dataSendFailed(QString("Partial write: %1 of %2 bytes").arg(bytesWritten).arg(wrappedData.size()));
        } else {
            m_socket->flush();
            // 注意：实际的bytesWritten信号会在onSocketBytesWritten中发出
        }
    } catch (const std::exception &e) {
        emit dataSendFailed(QString("Exception during send: %1").arg(e.what()));
    } catch (...) {
        emit dataSendFailed("Unknown exception during send");
    }
}

void NetworkWorker::setAutoReconnect(bool enabled, int intervalMs)
{
    m_autoReconnectEnabled.storeRelaxed(enabled ? 1 : 0);
    m_reconnectInterval = intervalMs;
    qDebug() << "NetworkWorker: Auto-reconnect" << (enabled ? "enabled" : "disabled") 
             << "interval:" << intervalMs << "ms";
}

void NetworkWorker::setHeartbeat(bool enabled, int intervalMs)
{
    m_heartbeatEnabled.storeRelaxed(enabled ? 1 : 0);
    m_heartbeatInterval = intervalMs;
    
    if (m_heartbeatTimer) {
        if (enabled && connectionState() == ConnectionState::Connected) {
            m_heartbeatTimer->start(intervalMs);
        } else {
            m_heartbeatTimer->stop();
        }
    }
    
    qDebug() << "NetworkWorker: Heartbeat" << (enabled ? "enabled" : "disabled") 
             << "interval:" << intervalMs << "ms";
}

void NetworkWorker::stop()
{
    qDebug() << "NetworkWorker: Stop requested";
    m_shouldStop.storeRelaxed(1);
    
    // 停止自动重连
    m_autoReconnectEnabled.storeRelaxed(0);
    
    // 断开连接
    disconnectFromServer();
}

NetworkWorker::ConnectionState NetworkWorker::connectionState() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_connectionState;
}

bool NetworkWorker::isConnected() const
{
    return connectionState() == ConnectionState::Connected;
}

void NetworkWorker::setConnectionState(NetworkWorker::ConnectionState state)
{
    bool stateChanged = false;
    {
        QMutexLocker locker(&m_stateMutex);
        if (m_connectionState != state) {
            m_connectionState = state;
            stateChanged = true;
        }
    }

    if (stateChanged) {
        qDebug() << "NetworkWorker: State changed to" << static_cast<int>(state);
        emit connectionStateChanged(state);
    }
}

// ========== 私有槽函数实现 ==========

void NetworkWorker::onSocketConnected()
{
    qDebug() << "NetworkWorker: Socket connected to" << m_host << ":" << m_port;

    // 停止连接超时定时器
    if (m_connectionTimeoutTimer) {
        m_connectionTimeoutTimer->stop();
    }

    setConnectionState(NetworkWorker::ConnectionState::Connected);
    stopReconnectTimer();

    // 启动心跳定时器
    if (m_heartbeatEnabled.loadRelaxed() && m_heartbeatTimer) {
        m_heartbeatTimer->start(m_heartbeatInterval);
    }
}

void NetworkWorker::onSocketDisconnected()
{
    qDebug() << "NetworkWorker: Socket disconnected";

    // 停止连接超时定时器
    if (m_connectionTimeoutTimer) {
        m_connectionTimeoutTimer->stop();
    }

    setConnectionState(ConnectionState::Disconnected);

    // 停止心跳定时器
    if (m_heartbeatTimer) {
        m_heartbeatTimer->stop();
    }

    // 清空读写策略缓冲区
    if (m_rwsStrategy) {
        m_rwsStrategy->clearBuffer();
    }

    // 启动自动重连（如果未停止且启用了自动重连）
    if (!m_shouldStop.loadRelaxed() && m_autoReconnectEnabled.loadRelaxed()) {
        startReconnectTimer();
    }
}

void NetworkWorker::onSocketError(QAbstractSocket::SocketError error)
{
    QString errorString = m_socket ? m_socket->errorString() : "Unknown error";
    qWarning() << "NetworkWorker: Socket error:" << error << errorString;

    // 停止连接超时定时器
    if (m_connectionTimeoutTimer) {
        m_connectionTimeoutTimer->stop();
    }

    setConnectionState(ConnectionState::Error);

    NetworkWorker::NetworkError netError = convertSocketError(error);
    emit networkError(netError, errorString);

    // 启动自动重连（除非是主动断开或正在停止）
    if (!m_shouldStop.loadRelaxed() && m_autoReconnectEnabled.loadRelaxed() &&
        error != QAbstractSocket::RemoteHostClosedError) {
        startReconnectTimer();
    }
}

void NetworkWorker::onSocketReadyRead()
{
    if (!m_socket || !m_rwsStrategy) {
        return;
    }

    QByteArray newData = m_socket->readAll();
    if (newData.isEmpty()) {
        return;
    }

    try {
        // 使用当前读写策略解析消息
        QList<QByteArray> messages = m_rwsStrategy->processReceivedData(newData);

        // 发送每个完整的消息
        for (const QByteArray &message : messages) {
            emit rawDataReceived(message);
        }
    } catch (const std::exception &e) {
        emit networkError(NetworkError::ProtocolError,
                         QString("RWS strategy processing error: %1").arg(e.what()));
    } catch (...) {
        emit networkError(NetworkError::ProtocolError, "Unknown RWS strategy processing error");
    }
}

void NetworkWorker::onSocketBytesWritten(qint64 bytes)
{
    emit dataSent(bytes);
}

void NetworkWorker::onReconnectTimer()
{
    if (m_shouldStop.loadRelaxed()) {
        return;
    }

    qDebug() << "NetworkWorker: Attempting to reconnect...";
    setConnectionState(ConnectionState::Reconnecting);

    if (m_socket) {
        // 启动连接超时定时器
        if (m_connectionTimeoutTimer) {
            m_connectionTimeoutTimer->start(m_connectionTimeout);
        }

        m_socket->connectToHost(m_host, m_port);
    }
}

void NetworkWorker::onHeartbeatTimer()
{
    if (connectionState() == ConnectionState::Connected) {
        sendHeartbeat();
    }
}

void NetworkWorker::onConnectionTimeout()
{
    qWarning() << "NetworkWorker: Connection timeout";

    if (m_socket && m_socket->state() == QAbstractSocket::ConnectingState) {
        m_socket->abort();  // 强制中断连接尝试
        emit networkError(NetworkError::ConnectionTimeout, "Connection timeout");

        // 启动自动重连
        if (!m_shouldStop.loadRelaxed() && m_autoReconnectEnabled.loadRelaxed()) {
            startReconnectTimer();
        }
    }
}

// ========== 私有辅助方法实现 ==========

void NetworkWorker::startReconnectTimer()
{
    if (m_reconnectTimer && !m_reconnectTimer->isActive() && !m_shouldStop.loadRelaxed()) {
        setConnectionState(ConnectionState::Reconnecting);
        m_reconnectTimer->start(m_reconnectInterval);
        qDebug() << "NetworkWorker: Reconnect timer started, interval:" << m_reconnectInterval << "ms";
    }
}

void NetworkWorker::stopReconnectTimer()
{
    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
        qDebug() << "NetworkWorker: Reconnect timer stopped";
    }
}

void NetworkWorker::sendHeartbeat()
{
    // 发送简单的心跳JSON消息
    QJsonObject heartbeat;
    heartbeat["Type"] = "Heartbeat";
    heartbeat["Timestamp"] = QDateTime::currentMSecsSinceEpoch();

    QJsonDocument doc(heartbeat);
    QByteArray heartbeatData = doc.toJson(QJsonDocument::Compact);

    // 直接发送心跳，不通过常规发送队列
    if (m_socket && m_socket->state() == QAbstractSocket::ConnectedState && m_rwsStrategy) {
        try {
            // 使用当前读写策略封装心跳消息
            QByteArray wrappedData = m_rwsStrategy->wrapMessage(heartbeatData);
            qint64 bytesWritten = m_socket->write(wrappedData);
            if (bytesWritten > 0) {
                m_socket->flush();
            }
        } catch (const std::exception &e) {
            qWarning() << "NetworkWorker: Heartbeat send failed:" << e.what();
        } catch (...) {
            qWarning() << "NetworkWorker: Heartbeat send failed with unknown exception";
        }
    }
}

NetworkWorker::NetworkError NetworkWorker::convertSocketError(QAbstractSocket::SocketError socketError)
{
    switch (socketError) {
        case QAbstractSocket::ConnectionRefusedError:
            return NetworkError::ConnectionRefused;
        case QAbstractSocket::RemoteHostClosedError:
            return NetworkError::NoError;  // 正常断开
        case QAbstractSocket::HostNotFoundError:
            return NetworkError::HostNotFound;
        case QAbstractSocket::SocketTimeoutError:
            return NetworkError::ConnectionTimeout;
        case QAbstractSocket::NetworkError:
        case QAbstractSocket::SocketAccessError:
        case QAbstractSocket::SocketResourceError:
        case QAbstractSocket::DatagramTooLargeError:
        case QAbstractSocket::AddressInUseError:
        case QAbstractSocket::SocketAddressNotAvailableError:
        case QAbstractSocket::UnsupportedSocketOperationError:
        case QAbstractSocket::ProxyAuthenticationRequiredError:
        case QAbstractSocket::SslHandshakeFailedError:
        case QAbstractSocket::UnfinishedSocketOperationError:
        case QAbstractSocket::ProxyConnectionRefusedError:
        case QAbstractSocket::ProxyConnectionClosedError:
        case QAbstractSocket::ProxyConnectionTimeoutError:
        case QAbstractSocket::ProxyNotFoundError:
        case QAbstractSocket::ProxyProtocolError:
        case QAbstractSocket::OperationError:
        case QAbstractSocket::SslInternalError:
        case QAbstractSocket::SslInvalidUserDataError:
        case QAbstractSocket::TemporaryError:
        default:
            return NetworkError::SocketError;
    }
}

void NetworkWorker::safeCleanupTimer(QTimer *&timer)
{
    if (timer) {
        timer->stop();
        timer->deleteLater();
        timer = nullptr;
    }
}

// ========== 读写策略管理方法实现 ==========

void NetworkWorker::setRWSStrategyType(IRWSStrategy::StrategyType type)
{
    qDebug() << "NetworkWorker: Setting RWS strategy type to" << static_cast<int>(type);

    // 创建新的配置
    RWSConfig newConfig = m_rwsConfig;
    newConfig.setStrategyType(type);

    // 切换策略
    switchRWSStrategyInternal(newConfig);
}

void NetworkWorker::setRWSStrategyConfig(const RWSConfig &config)
{
    qDebug() << "NetworkWorker: Setting RWS strategy config, type:" << static_cast<int>(config.strategyType());

    // 切换策略
    switchRWSStrategyInternal(config);
}

IRWSStrategy::StrategyType NetworkWorker::getCurrentStrategyType() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_rwsConfig.strategyType();
}

std::unique_ptr<IRWSStrategy> NetworkWorker::createRWSStrategy(const RWSConfig &config)
{
    try {
        auto strategy = RWSStrategyFactory::createStrategy(config);
        if (strategy) {
            qDebug() << "NetworkWorker: Successfully created RWS strategy:" << strategy->strategyName();
        } else {
            qWarning() << "NetworkWorker: Failed to create RWS strategy for type:" << static_cast<int>(config.strategyType());
        }
        return strategy;
    } catch (const std::exception &e) {
        qCritical() << "NetworkWorker: Exception creating RWS strategy:" << e.what();
        return nullptr;
    } catch (...) {
        qCritical() << "NetworkWorker: Unknown exception creating RWS strategy";
        return nullptr;
    }
}

void NetworkWorker::switchRWSStrategyInternal(const RWSConfig &config)
{
    // 确保在工作线程中执行
    if (QThread::currentThread() != thread()) {
        qWarning() << "NetworkWorker: switchRWSStrategyInternal called from wrong thread";
        return;
    }

    // 如果策略类型没有变化，只更新配置
    {
        QMutexLocker locker(&m_stateMutex);
        if (m_rwsConfig.strategyType() == config.strategyType()) {
            m_rwsConfig = config;
            qDebug() << "NetworkWorker: Updated RWS strategy config (same type)";
            return;
        }
    }

    // 创建新的策略实例
    auto newStrategy = createRWSStrategy(config);
    if (!newStrategy) {
        qCritical() << "NetworkWorker: Failed to create new RWS strategy, keeping current strategy";
        return;
    }

    // 保存旧策略的缓冲区数据（如果需要迁移）
    QByteArray oldBufferData;
    if (m_rwsStrategy) {
        // 注意：这里我们选择清空旧缓冲区，避免协议混乱
        // 在实际应用中，可能需要根据具体需求决定是否保留数据
        m_rwsStrategy->clearBuffer();
        qDebug() << "NetworkWorker: Cleared old strategy buffer during switch";
    }

    // 原子性地更新策略和配置
    {
        QMutexLocker locker(&m_stateMutex);
        m_rwsStrategy = std::move(newStrategy);
        m_rwsConfig = config;
    }

    qInfo() << "NetworkWorker: Successfully switched to RWS strategy:"
            << m_rwsStrategy->strategyName()
            << "type:" << static_cast<int>(m_rwsStrategy->strategyType());

    // 发出策略切换完成信号
    emit rwsStrategyChanged(m_rwsStrategy->strategyType(), m_rwsStrategy->strategyName());
}
