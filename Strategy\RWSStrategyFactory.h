#ifndef RWS_STRATEGY_FACTORY_H
#define RWS_STRATEGY_FACTORY_H

#include <memory>
#include "IRWSStrategy.h"
#include "RWSConfig.h"

/**
 * @brief 简化版读写策略工厂类
 */
class RWSStrategyFactory
{
public:
    /**
     * @brief 根据类型创建策略
     */
    static std::unique_ptr<IRWSStrategy> createStrategy(IRWSStrategy::StrategyType type);

    /**
     * @brief 根据配置创建策略
     */
    static std::unique_ptr<IRWSStrategy> createStrategy(const RWSConfig &config);

    /**
     * @brief 获取支持的策略类型列表
     */
    static QList<IRWSStrategy::StrategyType> getSupportedStrategyTypes();

    /**
     * @brief 检查策略类型是否支持
     */
    static bool isStrategySupported(IRWSStrategy::StrategyType type);

};

#endif // RWS_STRATEGY_FACTORY_H
