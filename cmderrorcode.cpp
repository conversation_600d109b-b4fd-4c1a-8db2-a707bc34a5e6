#include "cmderrorcode.h"
#include "cmd/errorcode.h"

CmdErrorCode CmdErrorCode::instance;

CmdErrorCode::~CmdErrorCode()
{

}

CmdErrorCode &CmdErrorCode::getInstance()
{
    return instance;
}
QString CmdErrorCode::ErroCode2QString(int errorcode)
{
    QString str="";
    switch(errorcode){
    case cmd::errcode::normal:
        break;
    case cmd::errcode::internalError:
        str=tr("internalError");
        break;
    case cmd::errcode::flowFormatError:
        str=tr("flowFormatError");
        break;
    case cmd::errcode::flowUnload:
        str=tr("flowUnload");
        break;
    case cmd::errcode::flowNotStopped:
        str=tr("flowNotStopped");
        break;
    case cmd::errcode::cmdUnsupport:
        str=tr("cmdUnsupport");
        break;
    case cmd::errcode::cmdFormatError:
        str=tr("cmdFormatError");
        break;
    case cmd::errcode::cmdParamError:
        str=tr("cmdParamError");
        break;
    case cmd::errcode::variableInvalid:
        str=tr("variableInvalid");
        break;
    case cmd::errcode::variableOperationUnsupported:
        str=tr("variableOperationUnsupported");
        break;
    case cmd::errcode::variableAssignFail:
        str=tr("variableAssignFail");
        break;
    case cmd::errcode::variableDuplicate:
        str=tr("variableDuplicate");
        break;
    case cmd::errcode::variableOperatorFail:
        str=tr("variableOperatorFail");
        break;
    case cmd::errcode::tcpServerExist:
        str=tr("tcpServerExist");
        break;
    case cmd::errcode::tcpServerNotExist:
        str=tr("tcpServerNotExist");
        break;
    case cmd::errcode::tcpServerNameEmpty:
        str=tr("tcpServerNameEmpty");
        break;
    case cmd::errcode::tcpServerListenFail:
        str=tr("tcpServerListenFail");
        break;
    case cmd::errcode::tcpServerWaitMsgTimeOut:
        str=tr("tcpServerWaitMsgTimeOut");
        break;
    case cmd::errcode::messageTargetError:
        str=tr("messageTargetError");
        break;
    case cmd::errcode::divisorZero:
        str=tr("divisorZero");
        break;
    case cmd::errcode::axisIndexInvaild:
        str=tr("axisIndexInvaild");
        break;
    case cmd::errcode::axisMoveError:
        str=tr("axisMoveError");
        break;
    case cmd::errcode::axisWaitStopError:
        str=tr("axisWaitStopError");
        break;
    case cmd::errcode::axisJogExceedMaxPos:
        str=tr("axisJogExceedMaxPos");
        break;
    case cmd::errcode::axisHomeError:
        str=tr("axisHomeError");
        break;
    case cmd::errcode::axisMoveTimeOut:
        str=tr("axisMoveTimeOut");
        break;
    case cmd::errcode::pointDuplicate:
        str=tr("pointDuplicate");
        break;
    case cmd::errcode::pointInvalid:
        str=tr("pointInvalid");
        break;
    case cmd::errcode::ioInvalid:
        str=tr("ioInvalid");
        break;
    case cmd::errcode::waitInportOvertime:
        str=tr("waitInportOvertime");
        break;
    case cmd::errcode::motionIDInvalid:
        str=tr("motionIDInvalid");
        break;
    case cmd::errcode::handleNotExist:
        str=tr("handleNotExist");
        break;
    case cmd::errcode::handleTypeError:
        str=tr("handleTypeError");
        break;
    case cmd::errcode::timeout:
        str=tr("timeout");
        break;
    case cmd::errcode::formatError:
        str=tr("formatError");
        break;
    default:
        str=QString::number(errorcode);
        break;
    }
    return str;
}

CmdErrorCode::CmdErrorCode()
{
}
